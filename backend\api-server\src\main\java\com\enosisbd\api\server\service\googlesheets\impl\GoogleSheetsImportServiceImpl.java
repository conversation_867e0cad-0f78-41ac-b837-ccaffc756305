package com.enosisbd.api.server.service.googlesheets.impl;

import com.enosisbd.api.server.dto.*;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsImportService;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsService;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.project.ProjectService;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import com.enosisbd.api.server.service.tree.ProjectTreeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class GoogleSheetsImportServiceImpl implements GoogleSheetsImportService {

    private final GoogleSheetsService googleSheetsService;
    private final ProjectService projectService;
    private final ModuleService moduleService;
    private final SubModuleService subModuleService;
    private final ProjectTreeService projectTreeService;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public GoogleSheetImportResponseDto importFromGoogleSheet(GoogleSheetImportRequestDto request, String userEmail)
            throws IOException, GeneralSecurityException {

        // Extract sheet ID from URL
        String sheetId = googleSheetsService.extractSheetId(request.getSheetUrl());

        // Get sheet names
        List<String> sheetNames = googleSheetsService.getSheetNames(sheetId, userEmail);
        if (sheetNames.isEmpty()) {
            throw new BadRequestRestException("No sheets found in the Google Sheet");
        }

        // Filter out excluded tabs
        sheetNames = filterExcludedTabs(sheetNames, request.getExcludedTabs());

        // Create project
        String projectName;
        if (StringUtils.hasText(request.getProjectName())) {
            projectName = request.getProjectName();
        } else {
            // Use Google Sheet file name if project name not provided
            projectName = googleSheetsService.getSheetFileName(sheetId, userEmail);
        }

        ProjectDto projectDto = new ProjectDto();
        projectDto.setName(projectName);
        projectDto.setGoogleSheetId(sheetId);
        projectDto.setGoogleSheetUrl(request.getSheetUrl());
        projectDto.setSubmoduleColumn(request.getSubmoduleColumn());
        projectDto.setSubmoduleStartRow(request.getSubmoduleStartRow());
        projectDto.setGoogleUserEmail(userEmail);
        projectDto.setExcludedTabs(request.getExcludedTabs());

        projectDto = projectService.add(projectDto);

        // Convert column letter to index (0-based)
        int columnIndex = googleSheetsService.columnLetterToIndex(request.getSubmoduleColumn());

        // Convert start row to 0-based index (subtract 1)
        int startRowIndex = request.getSubmoduleStartRow() - 1;
        if (startRowIndex < 0) {
            startRowIndex = 0;
        }

        // Process each sheet as a module
        for (String sheetName : sheetNames) {
            // Create module
            ModuleDto moduleDto = new ModuleDto();
            moduleDto.setName(sheetName);
            moduleDto.setProjectId(projectDto.getId());

            moduleDto = moduleService.add(moduleDto);

            // Get sheet data
            List<List<Object>> sheetData = googleSheetsService.getSheetData(sheetId, sheetName, userEmail);

            // Extract submodules
            List<SubModuleRangeDto> submoduleRanges = googleSheetsService.extractSubmodules(
                    sheetData, columnIndex, startRowIndex);

            // Create submodules
            for (SubModuleRangeDto submoduleRange : submoduleRanges) {
                SubModuleDto subModuleDto = new SubModuleDto();
                subModuleDto.setName(submoduleRange.getName());
                subModuleDto.setModuleId(moduleDto.getId());
                subModuleDto.setStartRow(submoduleRange.getStartRow());
                subModuleDto.setEndRow(submoduleRange.getEndRow());

                subModuleService.add(subModuleDto);
            }
        }

        // Get the project tree
        TreeNodeDto projectTree = projectTreeService.getProjectTree(projectDto.getId());

        return GoogleSheetImportResponseDto.builder()
                .project(projectDto)
                .projectTree(projectTree)
                .build();
    }

    /**
     * Filter out excluded tabs from the list of sheet names
     *
     * @param sheetNames The list of all sheet names
     * @param excludedTabs Comma-separated list of tab names to exclude
     * @return Filtered list of sheet names
     */
    private List<String> filterExcludedTabs(List<String> sheetNames, String excludedTabs) {
        if (excludedTabs == null || excludedTabs.trim().isEmpty()) {
            return sheetNames;
        }

        // Parse excluded tabs (case-insensitive, trimmed)
        List<String> excludedTabsList = Arrays.stream(excludedTabs.split(","))
                .map(String::trim)
                .filter(tab -> !tab.isEmpty())
                .map(String::toLowerCase)
                .collect(Collectors.toList());

        if (excludedTabsList.isEmpty()) {
            return sheetNames;
        }

        // Filter out excluded tabs
        return sheetNames.stream()
                .filter(sheetName -> !excludedTabsList.contains(sheetName.toLowerCase()))
                .collect(Collectors.toList());
    }
}
