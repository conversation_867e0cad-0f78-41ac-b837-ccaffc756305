package com.enosisbd.api.server.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for Google Sheet import request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoogleSheetImportRequestDto {

    @NotBlank(message = "Google Sheet URL cannot be blank")
    private String sheetUrl;

    @NotBlank(message = "Submodule column cannot be blank")
    @Pattern(regexp = "^[A-Z]$", message = "Submodule column must be a single uppercase letter (A-Z)")
    private String submoduleColumn;

    @NotNull(message = "Submodule start row cannot be null")
    private Integer submoduleStartRow;

    private String projectName; // Optional - if not provided, will use sheet name

    // If using OAuth, this will be populated with the user's Google email
    private String googleUserEmail;

    /**
     * Comma-separated list of tab names to exclude from project tree structure creation.
     */
    private String excludedTabs;
}
